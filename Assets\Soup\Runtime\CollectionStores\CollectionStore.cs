﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Soup
{
    public abstract class CollectionStore<T> : ScriptableObject, IList<T>, IPlayModeStateChangeReceiver
    {
        // Keep it protected for serialization in the Editor.
        protected readonly List<T> _list = new();

        public int Count => _list.Count;
        public bool IsReadOnly => false;

        public readonly EventRegistry<T> OnItemAdded = new();
        public readonly EventRegistry<IEnumerable<T>> OnItemsAdded = new();
        public readonly EventRegistry<T> OnItemRemoved = new();
        public readonly EventRegistry<T[]> OnItemsRemoved = new();

        public void Add(T item)
        {
            _list.Add(item);
            OnItemAdded.Invoke(item);
        }

        public void AddRange(IEnumerable<T> items)
        {
            var collection = items.ToArray();
            _list.AddRange(collection);
            OnItemsAdded.Invoke(collection);
        }

        public bool Remove(T item)
        {
            var result = _list.Remove(item);

            if (result)
                OnItemRemoved.Invoke(item);

            return result;
        }

        public void Insert(int index, T item)
        {
            _list.Insert(index, item);
            OnItemAdded.Invoke(item);
        }

        public void RemoveAt(int index)
        {
            T item = _list[index];
            _list.RemoveAt(index);
            OnItemRemoved.Invoke(item);
        }

        public void RemoveAll(Predicate<T> match)
        {
            T[] removed = _list.Where(x => match(x)).ToArray();
            _list.RemoveAll(match);
            OnItemsRemoved.Invoke(removed);
        }

        public T this[int index]
        {
            get => _list[index];
            set => _list[index] = value;
        }

        public IEnumerator<T> GetEnumerator() => _list.GetEnumerator();

        IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();

        public void Clear() => _list.Clear();

        public void CopyTo(T[] array, int arrayIndex) => _list.CopyTo(array, arrayIndex);

        public bool Contains(T item) => _list.Contains(item);

        public int IndexOf(T item) => _list.IndexOf(item);

        public void OnWillEnterPlayMode()
        {
        }

        public void OnExitingPlayMode()
        {
            // We always clear the collection and related event registries upon exiting play mode
            // to avoid keeping ghost objects and leak memory.
            OnItemAdded.Clear();
            OnItemsAdded.Clear();
            OnItemRemoved.Clear();
            OnItemsRemoved.Clear();
            Clear();
        }
    }
}