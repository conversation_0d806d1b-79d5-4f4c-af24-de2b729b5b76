﻿using UnityEditor;
using UnityEngine;
using System.Collections;
using System.Reflection;

namespace Soup
{
    [CustomEditor(typeof(ScriptableObject), true)]
    public class CollectionStoreEditor : Editor
    {
        private FieldInfo _listField;
        private IList _items;
        private bool _isCollectionStore;

        private void OnEnable()
        {
            var type = target.GetType();

            // Check inheritance from CollectionStore<>
            while (type != null)
            {
                if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(CollectionStore<>))
                {
                    _isCollectionStore = true;
                    break;
                }

                type = type.BaseType;
            }

            if (!_isCollectionStore) return;

            _listField = target.GetType().GetField("_list", BindingFlags.NonPublic | BindingFlags.Instance);

            if (_listField != null)
            {
                _items = _listField.GetValue(target) as IList;
            }
        }

        public override void OnInspectorGUI()
        {
            if (!_isCollectionStore)
            {
                base.OnInspectorGUI();
                return;
            }

            GUI.enabled = false;
            DrawScriptField();
            GUI.enabled = true;

            serializedObject.Update();

            if (_listField == null || _items == null)
            {
                EditorGUILayout.HelpBox("List field not found or is null.", MessageType.Warning);
                return;
            }

            EditorGUILayout.LabelField("List content:", EditorStyles.boldLabel);
            EditorGUI.indentLevel++;

            for (int i = 0; i < _items.Count; i++)
            {
                object element = _items[i];

                if (element is Object unityObj)
                {
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.ObjectField($"Element {i}", unityObj, unityObj.GetType(), allowSceneObjects: true);

                    if (GUILayout.Button("Select", GUILayout.Width(60))) // Add a button
                    {
                        if (unityObj != null)
                        {
                            Selection.activeObject = unityObj; // Select the object
                            EditorGUIUtility.PingObject(unityObj); // Optional: Ping the object in the editor
                        }
                    }

                    EditorGUILayout.EndHorizontal();
                }
                else
                {
                    string label = element != null ? element.ToString() : "null";
                    EditorGUILayout.LabelField($"Element {i}", label);
                }
            }

            EditorGUI.indentLevel--;

            serializedObject.ApplyModifiedProperties();
        }

        private void DrawScriptField()
        {
            MonoScript script = MonoScript.FromScriptableObject((ScriptableObject)target);
            EditorGUI.BeginDisabledGroup(true);
            EditorGUILayout.ObjectField("Script", script, typeof(MonoScript), false);
            EditorGUI.EndDisabledGroup();
        }
    }
}