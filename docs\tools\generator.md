# Generator

At some point, you may want to have an Event, a Collection Store, or a Value Store containing custom data. You can speed up the process of creating these by using the `Generator` tool.  
The Generator is a Unity editor window that prevents you from having to manually create these, reducing the boilerplate even more.

The Generator is located in the `Soup -> Generator` menu. It looks like this:

![Soup Generator](./images/soup-generator.jpg)

!> The Generator do not check for existing types, make sure to not create duplicates.

### Options

#### Asset Type

You can choose between `Event`, `Collection Store`, or `Value Store`:

![Soup Generator Type Dropdown](./images/soup-generator-dropdown.jpg)

#### Type Name

This is the name of the type that you want to generate. If you want to use a custom type, you'll have to create it first:

```csharp
[Serializable]
public struct MyCustomType
{
    public string Name;
}
```

You would then type `MyCustomType` in the `Type Name` field.

#### Type Namespace

This is the namespace that you want to use for the type. By default, it is simply `Soup`.

#### Path

This is the path where the assets will be created. By default, it is the Assets folder.  
You can browse to any folder in your project by clicking the `Browse` button.

