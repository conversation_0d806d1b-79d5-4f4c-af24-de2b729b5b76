﻿using System;
using UnityEngine;

namespace Soup
{
    /// <summary>
    /// Non-generic base class for all ValueStore types.
    /// Provides common functionality and allows for type-safe casting without reflection.
    /// </summary>
    [Serializable]
    public abstract class ValueStoreBase : ScriptableObject, IPlayModeStateChangeReceiver
    {
        [SerializeField]
        protected bool _resetAfterPlayModeExit = true;

        public abstract Type ValueType { get; }
        public abstract object GetValue();
        public abstract void SetValue(object value);

        public virtual void Awake()
        {
            // Prevents from resetting if no reference in a scene
            hideFlags = HideFlags.DontUnloadUnusedAsset;
        }

        public abstract void OnWillEnterPlayMode();
        public abstract void OnExitingPlayMode();
    }

    [Serializable]
    public abstract class ValueStore<T> : ValueStoreBase
    {
        private readonly EventRegistry<T> _eventRegistry = new();

        [SerializeField]
        private T _value;

        public T Value
        {
            get => _value;
            set
            {
                if (Value.Equals(value)) return;

                _value = value;
                NotifyChange();
            }
        }

        private T _initialValue;

        // Base class implementations
        public override Type ValueType => typeof(T);
        public override object GetValue() => _value;
        public override void SetValue(object value) => Value = (T)value;

        public EventSubscription AddListener(Action<T> listener) => _eventRegistry.AddListener(listener);

        public EventSubscription AddListener<TSelection>(Func<T, TSelection> selector, Action<TSelection> listener)
        {
            var currentValue = selector(Value);

            void Action(T _)
            {
                var newValue = selector(Value);

                if (!currentValue.Equals(newValue))
                {
                    currentValue = newValue;
                    listener?.Invoke(newValue);
                }
            }

            Action(Value);

            return _eventRegistry.AddListener(Action);
        }

        public void RemoveAllListeners() => _eventRegistry.Clear();

        public void NotifyChange() => _eventRegistry.Invoke(Value);

        public override void OnWillEnterPlayMode()
        {
            _initialValue = Value;
        }

        public override void OnExitingPlayMode()
        {
            // We always clear the collection and related event registries upon exiting play mode
            // to avoid keeping ghost objects and leak memory.
            RemoveAllListeners();

            if (_resetAfterPlayModeExit)
                Value = _initialValue;
        }

        private void OnValidate()
        {
            NotifyChange();
        }

        public override string ToString() => Value.ToString();
    }
}