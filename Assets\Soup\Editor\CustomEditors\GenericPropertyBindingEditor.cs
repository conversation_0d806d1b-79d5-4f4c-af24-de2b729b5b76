using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace Soup
{
    [CustomEditor(typeof(GenericPropertyBinding))]
    public class GenericPropertyBindingEditor : Editor
    {
        private SerializedProperty _valueStoreProperty;
        private SerializedProperty _propertyIndexProperty;
        private SerializedProperty _formatStringProperty;

        private List<PropertyDiscoveryHelper.PropertyInfo> _availableProperties;
        private string[] _propertyDisplayNames;
        private ValueStoreBase _lastValueStore;
        private bool _isInitializing = true;

        private void OnEnable()
        {
            _valueStoreProperty = serializedObject.FindProperty("_valueStore");
            _propertyIndexProperty = serializedObject.FindProperty("_propertyIndex");
            _formatStringProperty = serializedObject.FindProperty("_formatString");

            // Store the current ValueStore reference to detect actual changes
            _lastValueStore = _valueStoreProperty.objectReferenceValue as ValueStoreBase;
            _isInitializing = true;

            RefreshPropertyList();

            _isInitializing = false;
        }

        public override void OnInspectorGUI()
        {
            serializedObject.Update();

            // Draw ValueStore field
            EditorGUILayout.PropertyField(_valueStoreProperty, new GUIContent("Value Store"));

            var currentValueStore = _valueStoreProperty.objectReferenceValue as ValueStoreBase;

            // Check if ValueStore actually changed (not just during initialization)
            if (currentValueStore != _lastValueStore)
            {
                _lastValueStore = currentValueStore;
                RefreshPropertyList();

                // Only auto-select default property when user actually changes ValueStore
                // (not during editor initialization/reload)
                if (currentValueStore && !_isInitializing)
                {
                    var defaultIndex = PropertyDiscoveryHelper.GetDefaultPropertyIndex(currentValueStore.ValueType);
                    _propertyIndexProperty.intValue = defaultIndex;
                }
            }

            // Draw property selector
            if (currentValueStore && _availableProperties is { Count: > 0 })
            {
                DrawPropertySelector();
            }
            else if (currentValueStore)
            {
                EditorGUILayout.HelpBox("No properties found on ValueStore type.", MessageType.Warning);
            }
            else
            {
                EditorGUILayout.HelpBox("Assign a ValueStore to see available properties.", MessageType.Info);
            }

            // Draw format string field
            EditorGUILayout.PropertyField(_formatStringProperty, new GUIContent("Format String"));

            serializedObject.ApplyModifiedProperties();
        }

        private void RefreshPropertyList()
        {
            var valueStore = _valueStoreProperty.objectReferenceValue as ValueStoreBase;
            
            if (!valueStore)
            {
                _availableProperties = new List<PropertyDiscoveryHelper.PropertyInfo>();
                _propertyDisplayNames = Array.Empty<string>();
                return;
            }

            _availableProperties = PropertyDiscoveryHelper.DiscoverProperties(valueStore.ValueType);
            _propertyDisplayNames = new string[_availableProperties.Count];
            
            for (int i = 0; i < _availableProperties.Count; i++)
            {
                _propertyDisplayNames[i] = _availableProperties[i].DisplayName;
            }
        }

        private void DrawPropertySelector()
        {
            // Ensure the property index is within bounds but preserve valid selections
            if (_propertyIndexProperty.intValue < 0 || _propertyIndexProperty.intValue >= _availableProperties.Count)
            {
                // Only reset to 0 if the current index is completely invalid
                // This preserves user selections during property list refreshes
                _propertyIndexProperty.intValue = 0;
            }

            // Draw property dropdown
            var newIndex = EditorGUILayout.Popup("Property", _propertyIndexProperty.intValue, _propertyDisplayNames);

            if (newIndex != _propertyIndexProperty.intValue)
            {
                _propertyIndexProperty.intValue = newIndex;
                // Mark the object as dirty to ensure serialization
                EditorUtility.SetDirty(target);
            }
        }
    }
}
