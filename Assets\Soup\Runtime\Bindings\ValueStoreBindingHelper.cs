using System;
using System.Linq;
using System.Reflection;

namespace Soup
{
    /// <summary>
    /// Helper class for dynamic ValueStore binding operations.
    /// Encapsulates all reflection-based logic for binding to ValueStore properties and fields
    /// </summary>
    public static class ValueStoreBindingHelper
    {
        /// <summary>
        /// Dynamically subscribes to changes in a ValueStore using reflection-based selectors.
        /// This method handles the complex reflection logic that was previously in ValueStoreBase.
        /// </summary>
        /// <param name="valueStore">The ValueStore to bind to</param>
        /// <param name="selector">Delegate that selects a property/field from the ValueStore's value</param>
        /// <param name="listener">Delegate that handles changes to the selected property/field</param>
        /// <returns>EventSubscription that can be disposed to unsubscribe</returns>
        /// <exception cref="ArgumentException">Thrown when delegate types are invalid</exception>
        public static EventSubscription AddListenerWithSelector(
            ValueStoreBase valueStore,
            Delegate selector,
            Delegate listener)
        {
            if (!valueStore)
                throw new ArgumentNullException(nameof(valueStore));
            if (selector == null)
                throw new ArgumentNullException(nameof(selector));
            if (listener == null)
                throw new ArgumentNullException(nameof(listener));

            var selectorType = selector.GetType();
            var listenerType = listener.GetType();

            if (!IsValidDelegateTypes(valueStore, selectorType, listenerType, out var selectionType))
            {
                throw new ArgumentException($"Invalid delegate types for ValueStore {valueStore.GetType().Name}: " +
                                            $"selector={selectorType.Name}, listener={listenerType.Name}");
            }

            try
            {
                // Find the generic AddListener<TSelection> method on the concrete ValueStore type
                var genericMethod = FindGenericAddListenerMethod(valueStore, selectionType);

                // Invoke the method with the provided delegates
                return (EventSubscription)genericMethod.Invoke(valueStore, new object[] { selector, listener });
            }
            catch (Exception ex)
            {
                throw new ArgumentException(
                    $"Error invoking AddListener method on {valueStore.GetType().Name}: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Validates that the provided delegate types are compatible with ValueStore binding.
        /// </summary>
        private static bool IsValidDelegateTypes(
            ValueStoreBase valueStore,
            Type selectorType,
            Type listenerType,
            out Type selectionType)
        {
            selectionType = null;

            // Validate selector is Func<T, TSelection>
            if (!selectorType.IsGenericType || selectorType.GetGenericTypeDefinition() != typeof(Func<,>))
                return false;

            // Validate listener is Action<TSelection>
            if (!listenerType.IsGenericType || listenerType.GetGenericTypeDefinition() != typeof(Action<>))
                return false;

            var selectorArgs = selectorType.GetGenericArguments();
            var listenerArgs = listenerType.GetGenericArguments();

            // Validate selector input type matches ValueStore's value type
            var valueType = selectorArgs[0];
            if (valueType != valueStore.ValueType)
                return false;

            // Validate selector output type matches the listener input type
            selectionType = selectorArgs[1];
            var actionType = listenerArgs[0];

            return selectionType == actionType;
        }

        /// <summary>
        /// Finds and instantiates the generic AddListener method on the ValueStore.
        /// </summary>
        private static MethodInfo FindGenericAddListenerMethod(ValueStoreBase valueStore, Type selectionType)
        {
            // Look for the generic AddListener<TSelection> method
            var methods = valueStore.GetType().GetMethods(BindingFlags.Public | BindingFlags.Instance);
            var genericMethod = methods.FirstOrDefault(m =>
                m.Name == "AddListener" &&
                m.IsGenericMethodDefinition &&
                m.GetParameters().Length == 2);

            if (genericMethod == null)
            {
                throw new InvalidOperationException(
                    $"Could not find generic AddListener method on {valueStore.GetType().Name}");
            }

            // Make the method concrete with the selection type
            return genericMethod.MakeGenericMethod(selectionType);
        }
    }
}