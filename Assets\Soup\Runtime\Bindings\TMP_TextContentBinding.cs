using TMPro;
using UnityEngine;

namespace Soup
{
    [ExecuteInEditMode]
    [RequireComponent(typeof(TMP_Text))]
    [AddComponentMenu(Constants.BINDINGS_MENU_PATH + "TextMeshPro - Text Content Binding")]
    [DisallowMultipleComponent]
    public class TMP_TextContentBinding : MonoBehaviour
    {
        [SerializeField]
        private StringStore _stringStore;

        private TMP_Text _text;
        private EventSubscription _subscription;

        private void Awake()
        {
            _text = GetComponent<TMP_Text>();
        }

        private void OnEnable()
        {
            if (!_stringStore) return;

            _text.text = _stringStore.Value;
            _subscription = _stringStore.AddListener(OnFontSizeChanged);
        }

        private void OnDisable()
        {
            _subscription?.Dispose();
        }

        private void OnFontSizeChanged(string newText)
        {
            _text.text = newText;
        }

#if UNITY_EDITOR
        private void OnValidate()
        {
            if (!_stringStore) return;

            if (!_text)
                _text = GetComponent<TMP_Text>();

            _text.text = _stringStore.Value;
        }
#endif
    }
}