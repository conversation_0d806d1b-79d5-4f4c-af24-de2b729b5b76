# Philosophy

### ScriptableObject Architecture

[ScriptableObject](https://docs.unity3d.com/Manual/class-ScriptableObject.html) Architecture (SOA) is a design pattern that aims to facilitate scalable, maintainable, and testable game development using Unity.  
This concept was popularized by [<PERSON> in his 2017 GDC talk](https://www.youtube.com/watch?v=raQ3iHhE_Kk) where he introduced the concept of ScriptableObjects as the glue that makes it possible to create modular and data-driven systems within Unity.

### Credits

This project is inspired by the following libraries:
- [<PERSON>'s sample project](https://github.com/roboryantron/Unite2017)
- [ScriptableObject-Architecture](https://github.com/DanielEverland/ScriptableObject-Architecture)
- [Unity Atoms](https://unity-atoms.github.io/unity-atoms)
- [Soap](https://obvious-game.gitbook.io/soap)
- [Soda](http://soda.13pixels.de/)
