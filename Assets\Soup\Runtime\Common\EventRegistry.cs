using System;
using System.Collections.Generic;
using Object = UnityEngine.Object;

namespace Soup
{
    public abstract class EventRegistry
    {
        protected readonly List<Object> _listenersObjects = new();
        
        public IReadOnlyList<Object> ListenerObjects => _listenersObjects;
    }
    
    public class EventRegistry<T> : EventRegistry
    {
        private Action<T> _event;
        
        public EventSubscription AddListener(Action<T> listener)
        {
            _event += listener;

#if UNITY_EDITOR
            var listenerObj = listener.Target as Object;
            if (listenerObj)
                _listenersObjects.Add(listenerObj);
#endif

            return new EventSubscription(() => RemoveListener(listener));
        }

        private void RemoveListener(Action<T> listener)
        {
#if UNITY_EDITOR
            var listenerObj = listener.Target as Object;
            if (listenerObj)
                _listenersObjects.Remove(listenerObj);
#endif
            
            _event -= listener;
        }

        public void Clear()
        {
            _event = null;

#if UNITY_EDITOR
            _listenersObjects.Clear();
#endif
        }
        
        public void Invoke(T value) => _event?.Invoke(value);
    }
}