# Creating Assets

Soup assets are ScriptableObjects that serve as the foundation for the framework's data-driven architecture. This guide explains the different ways to create these assets in your Unity project.

## Creation Methods

There are two primary ways to create Soup assets:

### 1. Using Unity's Create Menu

The most straightforward method is to use Unity's built-in Create menu:

1. Right-click in the Project window
2. Navigate to `Create > Soup > [Category] > [Asset Type]`
3. Select the desired asset type

![Create Soup Asset Menu](images/soup-create-asset.jpg)

This will create a new asset of the selected type in the current folder.

### 2. Using the Inspector Create Button

When you have a serialized field in a MonoBehaviour that expects a Soup asset but hasn't been assigned yet, you'll see a convenient Create button in the Inspector:

1. Select the GameObject with the component referencing a Soup asset
2. Look for the empty field in the Inspector
3. Click the `Create` button that appears to the right of the field

![Create Soup Asset Button](images/soup-create-button.jpg)

This will create a new asset of the required type and automatically assign it to the field.

## Runtime Asset Creation

While most assets are created in the editor, you can also create them at runtime. This is useful if you want to use the event system without needing to create assets in the editor first.

```csharp
// Create a new IntStore at runtime
IntStore runtimeScore = ScriptableObject.CreateInstance<IntStore>();
runtimeScore.Value = 0;

// Use it like any other IntStore
runtimeScore.Value += 10;
```

?> Note: Assets created at runtime will not be saved to disk unless you explicitly use `AssetDatabase.CreateAsset()` in editor scripts.

## Custom Asset Types

Custom types that you created using the [Generator](/tools/generator) tool will also appear in the menu.

## Custom Drawers

Soup includes custom property drawers that enhance the Unity Inspector experience when working with Soup assets (as well as other ScriptableObjects):

- Inline creation buttons for referenced assets
- Foldout for quick access to asset properties
- Debug tools for events and value stores

## Version Control

Soup assets are Unity assets and will be tracked by your version control system.
