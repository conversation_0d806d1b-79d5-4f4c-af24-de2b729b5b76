using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Soup
{
    /// <summary>
    /// Helper class for discovering properties and fields on ValueStore types.
    /// Provides type-safe property information for editor dropdowns.
    /// </summary>
    public static class PropertyDiscoveryHelper
    {
        /// <summary>
        /// Information about a discoverable property or field
        /// </summary>
        public class PropertyInfo
        {
            public string Name { get; set; }
            public string DisplayName { get; set; }
            public Type Type { get; set; }
            public bool IsProperty { get; set; }
            public bool IsField { get; set; }
            public bool IsReadable { get; set; }

            public PropertyInfo(string name, string displayName, Type type, bool isProperty, bool isReadable)
            {
                Name = name;
                DisplayName = displayName;
                Type = type;
                IsProperty = isProperty;
                IsField = !isProperty;
                IsReadable = isReadable;
            }
        }

        /// <summary>
        /// Primitive types that should auto-select "Value" property
        /// </summary>
        private static readonly HashSet<Type> PrimitiveTypes = new()
        {
            typeof(int), typeof(float), typeof(double), typeof(bool), typeof(string),
            typeof(byte), typeof(sbyte), typeof(short), typeof(ushort), typeof(uint),
            typeof(long), typeof(ulong), typeof(char), typeof(decimal)
        };

        /// <summary>
        /// Discovers all public properties and fields on the given type
        /// </summary>
        /// <param name="valueType">The type to discover properties on</param>
        /// <returns>List of discoverable properties and fields</returns>
        public static List<PropertyInfo> DiscoverProperties(Type valueType)
        {
            if (valueType == null)
                return new List<PropertyInfo>();

            // Add a "Value" property for direct binding (always first)
            var properties = new List<PropertyInfo>
            {
                new("", "Value (Direct Binding)", valueType, true, true)
            };

            // Discover public properties
            var typeProperties = valueType.GetProperties(BindingFlags.Public | BindingFlags.Instance);
            foreach (var prop in typeProperties)
            {
                if (prop.CanRead)
                {
                    properties.Add(new PropertyInfo(
                        prop.Name,
                        $"{prop.Name} ({GetTypeDisplayName(prop.PropertyType)})",
                        prop.PropertyType,
                        true,
                        true));
                }
            }

            // Discover public fields
            var typeFields = valueType.GetFields(BindingFlags.Public | BindingFlags.Instance);
            foreach (var field in typeFields)
            {
                properties.Add(new PropertyInfo(
                    field.Name,
                    $"{field.Name} ({GetTypeDisplayName(field.FieldType)})",
                    field.FieldType,
                    false,
                    true));
            }

            return properties.OrderBy(p => p.Name == "" ? "" : p.Name).ToList();
        }

        /// <summary>
        /// Determines if the given type should auto-select the "Value" property
        /// </summary>
        /// <param name="valueType">The ValueStore's underlying type</param>
        /// <returns>True if should auto-select a "Value" property</returns>
        public static bool ShouldAutoSelectValue(Type valueType)
        {
            if (valueType == null)
                return false;

            return PrimitiveTypes.Contains(valueType) || valueType.IsEnum;
        }

        /// <summary>
        /// Gets the default property index for the given type
        /// </summary>
        /// <param name="valueType">The ValueStore's underlying type</param>
        /// <returns>Index of the default property (0 for "Value", or first available property)</returns>
        public static int GetDefaultPropertyIndex(Type valueType)
        {
            // For primitive types, always default to "Value" (index 0)
            if (ShouldAutoSelectValue(valueType))
                return 0;

            // For complex types, also default to "Value" but user can change
            return 0;
        }

        /// <summary>
        /// Gets a user-friendly display name for a type
        /// </summary>
        private static string GetTypeDisplayName(Type type)
        {
            if (type == typeof(int)) return "int";
            if (type == typeof(float)) return "float";
            if (type == typeof(double)) return "double";
            if (type == typeof(bool)) return "bool";
            if (type == typeof(string)) return "string";
            if (type == typeof(byte)) return "byte";
            if (type == typeof(sbyte)) return "sbyte";
            if (type == typeof(short)) return "short";
            if (type == typeof(ushort)) return "ushort";
            if (type == typeof(uint)) return "uint";
            if (type == typeof(long)) return "long";
            if (type == typeof(ulong)) return "ulong";
            if (type == typeof(char)) return "char";
            if (type == typeof(decimal)) return "decimal";

            return type.Name;
        }
    }
}