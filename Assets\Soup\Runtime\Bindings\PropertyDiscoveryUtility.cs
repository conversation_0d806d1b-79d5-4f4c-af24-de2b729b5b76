using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Soup
{
    /// <summary>
    /// Runtime utility for property discovery on ValueStore types.
    /// This is a simplified version of PropertyDiscoveryHelper that works in runtime.
    /// </summary>
    public static class PropertyDiscoveryUtility
    {
        /// <summary>
        /// Simple property information for runtime use
        /// </summary>
        public class PropertyInfo
        {
            public string Name { get; set; }
            public Type Type { get; set; }
            public bool IsProperty { get; set; }

            public PropertyInfo(string name, Type type, bool isProperty)
            {
                Name = name;
                Type = type;
                IsProperty = isProperty;
            }
        }

        /// <summary>
        /// Discovers all public properties and fields on the given type (runtime version)
        /// </summary>
        /// <param name="valueType">The type to discover properties on</param>
        /// <returns>List of discoverable properties and fields</returns>
        public static List<PropertyInfo> DiscoverProperties(Type valueType)
        {
            if (valueType == null)
                return new List<PropertyInfo>();

            // Add a "Value" property for direct binding (always first)
            var properties = new List<PropertyInfo>
            {
                new("", valueType, true)
            };

            // Discover public properties
            var typeProperties = valueType.GetProperties(BindingFlags.Public | BindingFlags.Instance);
            
            foreach (var prop in typeProperties)
            {
                if (prop.CanRead)
                {
                    properties.Add(new PropertyInfo(prop.Name, prop.PropertyType, true));
                }
            }

            // Discover public fields
            var typeFields = valueType.GetFields(BindingFlags.Public | BindingFlags.Instance);
            
            foreach (var field in typeFields)
            {
                properties.Add(new PropertyInfo(field.Name, field.FieldType, false));
            }

            return properties.OrderBy(p => p.Name == "" ? "" : p.Name).ToList();
        }


        /// <summary>
        /// Gets the property name by index
        /// </summary>
        /// <param name="properties">List of discovered properties</param>
        /// <param name="index">Index to get name for</param>
        /// <returns>Property name, or empty string for direct binding</returns>
        public static string GetPropertyNameByIndex(List<PropertyInfo> properties, int index)
        {
            if (index < 0 || index >= properties.Count)
                return "";

            return properties[index].Name;
        }
    }
}