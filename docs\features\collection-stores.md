# Collection Stores

Collection Stores are ScriptableObject-based containers that hold multiple values of a specific type. They provide a centralized way to manage collections of data that can be easily referenced and modified across your Unity project.

Soup provides several built-in Collection Store types:
- `GameObjectCollection` - for storing GameObject references
- `ComponentCollection` - for storing Component references

You can also create custom Collection Stores for your own data types using the [Generator tool](../tools/generator.md).

## Play Mode Behavior

Collection Stores automatically clear their contents when exiting Play Mode to prevent references to destroyed objects from persisting. This ensures a clean state when you start Play Mode again.

## Referencing Collection Stores

To use a Collection Store in your MonoBehaviour, simply add a serialized field of the appropriate type:

```csharp
public class MyBehaviour : MonoBehaviour
{    
    [SerializeField] private GameObjectCollection _enemies;
    [SerializeField] private ComponentCollection _playerWeapons;    
    
    // Custom type example    
    [SerializeField] private ItemCollection _inventory;
}
```

Then drag and drop the Collection Store asset into the field in the Inspector. This creates a reference to the shared collection that can be accessed from anywhere in your project.

## Working with Collections

Collection Stores implement the `IList<T>` interface, allowing you to use them like standard C# collections:

```csharp
// Adding items
_enemies.Add(enemyGameObject);

// Adding multiple items at once
_playerWeapons.AddRange(GetComponentsInChildren<Weapon>());

// Accessing items by indexGameObject 
firstEnemy = _enemies[0];

// Removing items
_enemies.Remove(enemyGameObject);_enemies.RemoveAt(0);

// Checking if an item exists
if (_enemies.Contains(enemyGameObject)) {    
    Debug.Log("Enemy is in the collection");
}

// Getting the count
int enemyCount = _enemies.Count;

// Iterating through items
foreach (var enemy in _enemies) {    
    enemy.SetActive(true);
}

// Using LINQ
var activeEnemies = _enemies.Where(e => e.activeSelf);
```

## Events

Collection Stores provide a powerful event system that allows you to react to collection changes. There are several event types you can subscribe to:

### Item Added

```csharp
// Subscribe to be notified when a single item is added
EventSubscription subscription = _enemies.OnItemAdded.AddListener(enemy => {    
    Debug.Log($"Enemy added: {enemy.name}");
    InitializeEnemy(enemy);
});

// Later, when you no longer need the listener
subscription.Dispose();
```

### Multiple Items Added
```csharp
EventSubscription subscription = _enemies.OnItemsAdded.AddListener(newEnemies => {    
    Debug.Log($"Added {newEnemies.Count()} new enemies");
    foreach (var enemy in newEnemies) {        
        InitializeEnemy(enemy);
    }
});
```

### Item Removed

```csharp
EventSubscription subscription = _enemies.OnItemRemoved.AddListener(enemy => {
    Debug.Log($"Enemy removed: {enemy.name}");
    CleanupEnemy(enemy);
});
```

### Multiple Items Removed

```csharp
EventSubscription subscription = _enemies.OnItemsRemoved.AddListener(removedEnemies => {
    Debug.Log($"Removed {removedEnemies.Length} enemies");    
    foreach (var enemy in removedEnemies) {
        CleanupEnemy(enemy);
    }
});
```

## Advanced Operations

Collection Stores support more advanced operations like filtering and batch modifications:

```csharp
// Remove all items matching a condition
_enemies.RemoveAll(enemy => enemy.GetComponent<Health>().IsDead);

// Combine with LINQ for powerful queries
var weakEnemies = _enemies    
    .Where(e => e.GetComponent<Health>().CurrentHealth < 20)
    .ToArray();
```

## Best Practices

### Immutable Collection Items

When storing custom types in collections, consider making them immutable:
```csharp
// Define your data as a record
public record Item(string Name, int Value, ItemType Type);

// Create a Collection Store for it
[CreateAssetMenu(menuName = "Soup/Collections/Item")]
public class ItemCollection : CollectionStore<Item> { }

// Usage with non-destructive updates
void UpgradeItem(Item item, int additionalValue) {    
    int index = _inventory.IndexOf(item);
    
    if (index >= 0) {        
        // Create a new item with updated value
        _inventory[index] = item with { Value = item.Value + additionalValue };
    }
}
```
