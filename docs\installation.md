# Installation

Welcome! 👋  
There are two ways to add Soup to your project.

!> Before installing Soup, please make sure your project uses Unity 2022.3 or later.

### Using the Unity Package Manager

1. Open the Package Manager in Unity (Window > Package Manager)
2. Click the add button in the top left corner, then click add package from git URL
3. Paste the following URL `https://github.com/Sov3rain/soup.git` into the text field and click Add

> If you want to use a specific release, you can append the release tag to the URL, like this: `https://github.com/Sov3rain/soup.git#1.0.0`

### Using the UnityPackage archive

1. Download the latest release from the [releases page](https://github.com/Sov3rain/soup/releases)
2. Drop the downloaded archive into your project's Assets folder

### Updating

If you have installed Soup using the UnityPackage archive, you can update it by downloading the latest UnityPackage in the [releases page](https://github.com/Sov3rain/soup/releases) and dropping it into your project's Assets folder.

If you have installed Soup using the Unity Package Manager, you can update it by re-add the same Git url in the Package Manager.

Finally, if you tagged a specific release in the url, you can update it by opening the `manifest.json` file in your project's Packages folder and changing the tag in the git url:

```json
{
  "dependencies": {
    "com.sov3rain.soup": "https://github.com/Sov3rain/soup.git#1.0.0"
  }
}
```