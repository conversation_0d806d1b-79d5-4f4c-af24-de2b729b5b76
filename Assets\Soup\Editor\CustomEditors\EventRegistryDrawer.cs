using System.Reflection;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;

namespace Soup
{
    public abstract class EventRegistryDrawer : Editor
    {
        protected FieldInfo _eventRegistryField;

        protected virtual void DrawListeners()
        {
            GUILayout.Space(10);

            if (EditorApplication.isPlaying)
            {
                if (_eventRegistryField != null &&
                    _eventRegistryField.GetValue(target) is EventRegistry eventRegistry)
                {
                    GUILayout.Label($"Listeners: {eventRegistry.ListenerObjects.Count}", EditorStyles.boldLabel);

                    foreach (var listener in eventRegistry.ListenerObjects)
                    {
                        EditorGUILayout.BeginHorizontal();
                        EditorGUILayout.ObjectField(listener, typeof(Object), true);

                        if (GUILayout.Button("Select", GUILayout.Width(60)))
                        {
                            if (listener is GameObject go)
                            {
                                Selection.activeGameObject = go;
                                EditorGUIUtility.PingObject(go);
                            }
                            else if (listener is Component component)
                            {
                                Selection.activeGameObject = component.gameObject;
                                EditorGUIUtility.PingObject(component.gameObject);
                            }
                        }

                        EditorGUILayout.EndHorizontal();
                    }
                }
                else
                {
                    GUILayout.Label("No listeners");
                }
            }
            else
            {
                GUILayout.Label("Listeners:", EditorStyles.boldLabel);
                GUILayout.Label("(Play mode to view listeners)");
            }
        }
    }
}