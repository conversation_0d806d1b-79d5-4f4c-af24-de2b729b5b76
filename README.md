# 🥣 SOUP

**SOUP** (Scriptable Object Utility Pack) is a lightweight, modular, and extensible framework for Unity that harnesses the power of **Scriptable Objects** to help you structure your projects cleanly and efficiently.

> 🍜 *Trash your spaghetti code and get a warm bowl of SOUP*

## 🚀 Why SOUP?

- ✅ **Modular architecture** built on Scriptable Objects  
- ✅ **Zero external dependencies**, plug-and-play ready  
- ✅ **Data-driven design**, great for non-tech team members  
- ✅ **Extensible**, without boilerplate  
- ✅ **Declarative approach** for decoupled, maintainable systems

## 📦 Key Features

- 🔧 Centralized state and event management  
- 🎯 Dependency injection via ScriptableObject assets  
- 🧱 Editor-configurable, reusable components
- 💻 Coder-friendly, for those who want to get their hands dirty

## 📁 Installation

1. Open the Package Manager in Unity
2. Click the `+` button in the top left corner, then select `Add package from git URL...`
3. Paste the following URL into the text field: `https://github.com/soup-org/soup.git`  
   > Alternatively, you can download the latest release from the [releases page](https://github.com/soup-org/soup/releases).
4. Start cooking 🎉

## 🧠 Usage Example

```csharp
[CreateAssetMenu(menuName = "SOUP/Events/GameEvent")]
public class GameEvent : ScriptableObject {
    public UnityEvent onRaised;
    public void Raise() => onRaised?.Invoke();
}
```

## 📚 Documentation

> Full documentation is simmering... 🍲  
> In the meantime, check out the examples in `/Samples`.

## ❤️ Contributing

Want to spice things up? Open an issue or submit a pull request! All contributions are welcome.
