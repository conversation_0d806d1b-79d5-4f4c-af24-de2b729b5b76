# Overview

Soup is a library that aims to make it easier to create data-driven systems in Unity. It encourages you to think about your data in a different way, to link it in a different way, to create a more modular and scalable architecture.

Accessing your data is primarily done through editor field references, and interacting with it can be done either in code or in the Unity Inspector.

Let's take a quick tour of the main building blocks of Soup.

## Value Stores

[Value Stores](/features/value-stores) are a container for a single value, in form of a [ScriptableObject](https://docs.unity3d.com/Manual/class-ScriptableObject.html).

Because they are ScriptableObjects, they are not bound to a specific GameObject, and scene. They are shared pieces of data, easy to inject via the Unity Inspector. Soup provides a set of built-in Value Stores, such as `IntValueStore`, `FloatValueStore`, `StringValueStore`, etc. The first step for using a Value Store is to create an asset of the desired type, and then drag it into the field of your MonoBehaviour.

Each Value Store provides an event that will be called when the value changes. This makes possible to write more reactive code, and to avoid having to manually check for changes.

?> For more information about creating Assets, please refer to the [Creating Assets](/features/creating-assets) section.

## Collection Stores

[Collection Stores](/features/collection-stores) are a container for storing multiple values. Each collection comes with a set of events that you can subscribe to:
- An item was added
- An item was removed
- An item was changed
- Multiple items were added
- Multiple items were removed

Soup provides a set of built-in `CollectionStores`, such as `GameObjectCollection` and `ComponentCollection`.

## Events

An [Event](/features/events) is a way to notify subscribers when something happens. Event are also ScriptableObjects, and can be created in the same way as `ValueStores` and `CollectionStores`. For debugging purposes, you can invoke an event from the Unity Inspector.

## Listeners

Listeners are the other half of the event system. Each Event type has its own Listener type, you can't mix them. They are not ScriptableObjects, they are regular components living in the scene.  
You can add a Listener component to any GameObject, then dragging the desired Event into the `Event` field.

From there, use the provided [UnityEvent](https://docs.unity3d.com/6000.1/Documentation/Manual/unity-events.html) to configure your callbacks.

?> For more information about Listeners, please refer to the [Events](/features/events) section.

## Bindings

[Bindings](/features/bindings) are components that create reactive connections between Value Stores and Unity UI elements. They automatically update UI properties when the underlying data changes, enabling a truly data-driven approach to UI development.

For example, you can use `GraphicColorBinding` to bind a `ColorStore` to any UI Graphic component (Image, Text, TextMeshPro, etc.), and whenever the ColorStore value changes, the UI element's color will automatically update. This eliminates the need to manually sync UI with your data and makes it easy to implement features like theme systems, accessibility options, and dynamic UI states.
