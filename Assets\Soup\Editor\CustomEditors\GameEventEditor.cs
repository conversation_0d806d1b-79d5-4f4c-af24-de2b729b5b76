﻿using UnityEditor;
using UnityEngine;
using System.Reflection;

namespace Soup
{
    [CustomEditor(typeof(GameEvent<>), true)]
    public class GameEventEditor : EventRegistryDrawer
    {
        private MethodInfo _invokeMethod;

        private void OnEnable()
        {
            _invokeMethod = target.GetType().BaseType?.GetMethod("Invoke", BindingFlags.Instance | BindingFlags.Public);
            _eventRegistryField = target.GetType().BaseType
                ?.GetField("_eventRegistry", BindingFlags.Instance | BindingFlags.NonPublic);
        }

        public override void OnInspectorGUI()
        {
            GUI.enabled = EditorApplication.isPlaying;
            base.OnInspectorGUI();

            GUILayout.Space(10);

            var property = serializedObject.FindProperty("_debugValue");

            if (GUILayout.Button("Invoke", GUILayout.Height(25)))
            {
                _invokeMethod.Invoke(target, new[] { GetDebugValue(property) });
            }

            GUI.enabled = true;

            DrawListeners();
        }

        private static object GetDebugValue(SerializedProperty property)
        {
            var type = property.serializedObject.targetObject.GetType();
            var field = type.GetField("_debugValue", BindingFlags.Instance | BindingFlags.NonPublic);
            return field?.GetValue(property.serializedObject.targetObject);
        }
    }
}
