﻿using UnityEngine;
using UnityEngine.Events;

namespace Soup
{
    public abstract class GameEventListener<TGameEvent, T> : MonoBehaviour
        where TGameEvent : GameEvent<T>
    {
        [SerializeField]
        private TGameEvent _gameEvent;

        [Header("Event")]
        [SerializeField]
        private UnityEvent<T> _response;

        private EventSubscription _eventSubscription;

        private void Awake()
        {
            _eventSubscription = _gameEvent.AddListener(OnRaise);
        }

        private void OnDestroy()
        {
            _eventSubscription.Dispose();
        }

        private void OnRaise(T value)
        {
            _response?.Invoke(value);
        }
    }
}