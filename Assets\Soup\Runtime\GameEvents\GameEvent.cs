﻿using System;
using UnityEngine;

namespace Soup
{
    public abstract class GameEvent<T> : ScriptableObject, IPlayModeStateChangeReceiver
    {
        [SerializeField, NonExtended] protected T _debugValue;

        private readonly EventRegistry<T> _eventRegistry = new();

        public void Invoke(T value) => _eventRegistry?.Invoke(value);

        public EventSubscription AddListener(Action<T> action) => _eventRegistry.AddListener(action); 

        public void RemoveAllListeners() => _eventRegistry.Clear();
        
        public void OnWillEnterPlayMode()
        {
        }

        public void OnExitingPlayMode()
        {
            // We always clear the event registry upon exiting play mode
            // avoid keeping ghost objects and leak memory.
            _eventRegistry.Clear();
        }
    }

    [CreateAssetMenu(menuName = "Soup/Events/Game Event", fileName = "Game Event")]
    public class GameEvent : GameEvent<Void>
    {
    }
}