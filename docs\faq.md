# Frequently Asked Questions

**Q: Why are my event listeners not being listed in the Event's inspector?**  
A: Anonymous lambdas defined within a method don't inherently belong to a specific object instance unless they capture it in some way. To make your lambda listeners visible in the Event inspector, explicitly capture `this` in your lambda:

```csharp
// Will NOT be visible in the event inspector
_intEvent.AddListener(value => print(value));

// Will be visible in the event inspector
_intEvent.AddListener(value => {
    // Capture this explicitly
    _ = this;
    print(value);
});
```

---

**Q: Why the listeners are not notified when I change a Value's property?**  
A: Value Stores use the `Equals` method to determine if a value has changed. When working with reference types (like custom classes), modifying properties directly won't trigger the equality check. Instead, you need to create a new instance or clone the existing one and assign it to the Value property.

```csharp
// This will NOT trigger listeners
_playerStateStore.Value.Level = 10;

// Instead, create a new instance or clone the existing one
var newState = _playerStateStore.Value;
newState.Level = 10;
_playerStateStore.Value = newState;
```

See [Value Stores: Value Types vs Reference Types](/features/value-stores#value-types-vs-reference-types) for more information.

---
