# Events

Events are ScriptableObject-based assets that allow different parts of your game to communicate without direct references to each other. They provide a powerful way to decouple systems while maintaining clear communication channels.

## Event Assets

Soup provides several built-in Event types:
- `GameEvent` - for simple notifications without data
- `IntEvent` - for events with integer data
- `FloatEvent` - for events with float data
- `StringEvent` - for events with string data
- `Vector2Event` - for events with Vector2 data
- `Vector3Event` - for events with Vector3 data
- `GameObjectEvent` - for events with GameObject references
- `ComponentEvent` - for events with Component references

You can also create custom Event types for your own data using the [Generator tool](../tools/generator.md).

## Creating Events

Events can be created through the Unity menu:
1. Right-click in the Project window
2. Select `Create > Soup > Events > [Event Type]`

Each event is a ScriptableObject asset that can be referenced by any script in your project.

## Invoking Events

To invoke (raise) an event, call the `Invoke` method with the appropriate value:

```csharp
// For a simple GameEvent (no data)
[SerializeField] private GameEvent _onPlayerDeath;

void Die() {
    _onPlayerDeath.Invoke(default); // GameEvent uses Void type
}

// For events with data
[SerializeField] private IntEvent _onScoreChanged;
[SerializeField] private Vector3Event _onPlayerMoved;

void AddScore(int points) {
    _score += points;
    _onScoreChanged.Invoke(_score);
}

void Move(Vector3 position) {
    transform.position = position;
    _onPlayerMoved.Invoke(position);
}
```

## Event Listeners

Event Listeners are MonoBehaviour components that respond to events. Each Event type has a corresponding Listener type:
- `GameEventListener` - responds to `GameEvent`
- `IntListener` - responds to `IntEvent`
- `FloatListener` - responds to `FloatEvent`
- `StringListener` - responds to `StringEvent`
- `Vector2Listener` - responds to `Vector2Event`
- `Vector3Listener` - responds to `Vector3Event`
- `GameObjectListener` - responds to `GameObjectEvent`
- `ComponentListener` - responds to `ComponentEvent`

### Adding Listeners to GameObjects

1. Select a GameObject in your scene
2. Add the appropriate Listener component via `Add Component`
3. Assign the Event asset to the `Channel` field
4. Configure responses using the Unity Event in the inspector

### Configuring Responses

Each Listener has a UnityEvent that is invoked when the event is raised. You can:
- Call methods on components in the scene
- Set properties or fields
- Enable/disable GameObjects
- Trigger animations, sounds, or other effects

## Listening in Code

You can also listen for events directly in your code:

```csharp
[SerializeField] private IntEvent _onScoreChanged;
[SerializeField] private GameEvent _onGameStart;

private EventSubscription _scoreSubscription;
private EventSubscription _gameStartSubscription;

private void Awake() {
    // Add listeners
    _scoreSubscription = _onScoreChanged.AddListener(UpdateScoreUI);
    _gameStartSubscription = _onGameStart.AddListener(OnGameStart);
}

private void OnDestroy() {
    // Dispose subscriptions to prevent memory leaks
    _scoreSubscription.Dispose();
    _gameStartSubscription.Dispose();
}

private void UpdateScoreUI(int newScore) {
    _scoreText.text = $"Score: {newScore}";
}

private void OnGameStart(Void _) {
    // Initialize game state
}
```

The `AddListener` method returns an `EventSubscription` object that you can use to unsubscribe from the event later by calling its `Dispose` method. This is the recommended approach for managing event subscriptions in code.

You can also remove all listeners at once by calling the `RemoveAllListeners` method on the event:

```csharp
_onScoreChanged.RemoveAllListeners();
```

## Debugging Events

Events include built-in debugging features:
- In Play Mode, you can see all active listeners in the Event's inspector
- You can manually invoke events from the inspector using the `Invoke` button
- Each Event has a `_debugValue` field that lets you set the value to use when invoking from the inspector

## Best Practices

For complex data, consider creating custom event types with immutable data:

```csharp
// Define your data as a record
public record DamageInfo(int Amount, DamageType Type, GameObject Source);

// Create an Event for it
[CreateAssetMenu(menuName = "Soup/Events/Damage Info")]
public class DamageInfoEvent : GameEvent<DamageInfo> { }

// Usage
_onDamaged.Invoke(new DamageInfo(10, DamageType.Fire, attacker));
```
